import React from 'react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useAdminDeposit } from '../hooks/useAdminDeposit';
import { DepositFilters } from './DepositFilters';
import { DepositTables } from './DepositTables';
import { ExportButtons } from './ExportButtons';
import { formatDateJapan } from '@/utils/dateUtils';

interface AdminDepositPageProps {
  agxMerchantNo: string;
}

export const AdminDepositPage: React.FC<AdminDepositPageProps> = ({ agxMerchantNo }) => {
  const {
    data,
    dates,
    transferDate,
    areaFilters,
    filteredAreas,
    filteredSubAreas,
    filteredMerchants,
    isLoading,
    filterLoading,
    error,
    dlEnable,
    handleAreaFiltersChange,
    handleTransferDateChange,
    handleSearch,
    handleExportPDF
  } = useAdminDeposit(agxMerchantNo);



  if (isLoading && !data) {
    return <LoadingSpinner />;
  }

  return (
    <div className="xl:px-4 pt-6 pb-2">
      <div className="flex items-center pb-4 gap-8 border-b border-[#6F6F6E] xl:flex-row flex-col">
        {/* Transfer Date Filter */}
        <div className='flex items-center md:gap-8 gap-2'>
          <div className="flex items-center md:gap-4 gap-2">
            <Label htmlFor="transfer-date" className="text-[#6F6F6E] text-2xl md:px-4">
              振込日
            </Label>
            <Select
              value={transferDate}
              onValueChange={handleTransferDateChange}
            >
              <SelectTrigger id="transfer-date" className="w-full px-10 text-xl text-[#6F6F6E]">
                <SelectValue placeholder="振込日を選択" />
              </SelectTrigger>
              <SelectContent className='text-[#6F6F6E]'>
                {dates.map((date, index) => (
                  <SelectItem key={index} value={date} className='text-xl'>
                    {formatDateJapan(date)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {data && (
            <ExportButtons
              data={data}
              transferDate={transferDate}
              dlEnable={dlEnable}
              onExportPDF={handleExportPDF}
            />
          )}
        </div>
        <span className="text-2xl ">
          利用期間｜20xx年mm月dd日〜20xx年mm月dd日
        </span>
      </div>
      {error && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {dates.length > 0 ? (
        <div>
          {/* Filters */}
          <DepositFilters
            filters={areaFilters}
            onFiltersChange={handleAreaFiltersChange}
            onSearch={handleSearch}
            filteredAreas={filteredAreas}
            filteredSubAreas={filteredSubAreas}
            filteredMerchants={filteredMerchants}
          />

          {/* Tables */}
          {/* <div className="flex justify-center items-center">
            <LoadingSpinner className='min-h-[calc(100vh-520px)]'/>
          </div> */}
          {filterLoading ? (
            <div className="flex justify-center items-center">
              <LoadingSpinner className='min-h-[calc(100vh-511px)]' />
            </div>
          ) : (
            <DepositTables
              data={data}
              areaFilters={areaFilters}
              transferDate={transferDate}
              switchLayoutDate="2023-11-05"
            />
          )}
        </div>
      ) : (
        <div className="flex justify-center items-center py-12">
          <h2 className="text-xl text-gray-600">振込データがありません。</h2>
        </div>
      )}
    </div>
  );
};
